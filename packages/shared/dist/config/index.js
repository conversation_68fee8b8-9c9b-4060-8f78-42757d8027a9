"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiConfig = exports.workerConfig = exports.config = void 0;
exports.validateConfig = validateConfig;
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
// Find the root directory (where package.json with workspaces exists)
function findRootDir() {
    let currentDir = __dirname;
    while (currentDir !== path_1.default.dirname(currentDir)) {
        const packageJsonPath = path_1.default.join(currentDir, "package.json");
        try {
            const packageJson = require(packageJsonPath);
            if (packageJson.workspaces) {
                return currentDir;
            }
        }
        catch (e) {
            // Continue searching
        }
        currentDir = path_1.default.dirname(currentDir);
    }
    // Fallback to current working directory
    return process.cwd();
}
const rootDir = findRootDir();
// Load environment variables from root .env files
const envFile = process.env.NODE_ENV === "production"
    ? ".env.production"
    : process.env.NODE_ENV === "development"
        ? ".env.development"
        : ".env";
dotenv_1.default.config({ path: path_1.default.resolve(rootDir, envFile) });
dotenv_1.default.config({ path: path_1.default.resolve(rootDir, ".env") }); // Fallback to default .env
exports.config = {
    NODE_ENV: process.env.NODE_ENV || "development",
    WORKER_PORT: parseInt(process.env.WORKER_PORT || "3000", 10),
    API_PORT: parseInt(process.env.API_PORT || "8001", 10),
    REDIS_URL: process.env.REDIS_URL || "redis://localhost:6379",
    MONGODB_URI: process.env.MONGODB_URI || "mongodb://localhost:27017/marketdata",
    API_KEY: process.env.API_KEY || "",
    QUIDAX_BASE_URL: process.env.QUIDAX_BASE_URL || "https://www.quidax.com/api/v1",
    UPDATE_INTERVAL_MS: parseInt(process.env.UPDATE_INTERVAL_MS || "10000", 10),
    REDIS_PUBLISH_INTERVAL_SECONDS: parseInt(process.env.REDIS_PUBLISH_INTERVAL_SECONDS || "5", 10),
    MONGO_SAVE_CRON: process.env.MONGO_SAVE_CRON || "0 0 * * *",
    LOG_LEVEL: process.env.LOG_LEVEL || "info",
};
// Validation
function validateConfig() {
    const requiredFields = [];
    // In production, all fields are required
    if (exports.config.NODE_ENV === "production") {
        requiredFields.push("REDIS_URL", "MONGODB_URI", "API_KEY", "QUIDAX_BASE_URL");
    }
    else {
        // In development, only QUIDAX_BASE_URL is required
        requiredFields.push("QUIDAX_BASE_URL");
    }
    const missingFields = requiredFields.filter((field) => !exports.config[field]);
    if (missingFields.length > 0) {
        if (exports.config.NODE_ENV === "production") {
            throw new Error(`Missing required environment variables: ${missingFields.join(", ")}`);
        }
        else {
            console.warn(`Missing environment variables: ${missingFields.join(", ")} - using defaults for development`);
        }
    }
}
// Export individual configs for backward compatibility
exports.workerConfig = {
    PORT: exports.config.WORKER_PORT,
    REDIS_URL: exports.config.REDIS_URL,
    MONGODB_URI: exports.config.MONGODB_URI,
    QUIDAX_BASE_URL: exports.config.QUIDAX_BASE_URL,
    API_KEY: exports.config.API_KEY,
    UPDATE_INTERVAL_MS: exports.config.UPDATE_INTERVAL_MS,
};
exports.apiConfig = {
    PORT: exports.config.API_PORT,
    REDIS_URL: exports.config.REDIS_URL,
};
//# sourceMappingURL=index.js.map