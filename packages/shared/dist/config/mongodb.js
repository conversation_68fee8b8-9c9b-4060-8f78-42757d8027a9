"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AggregatedMarketDataModel = exports.MarketDataModel = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const index_1 = require("./index");
// MongoDB connection options
const mongoOptions = {
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
};
// Connect to MongoDB
const connectMongoDB = async () => {
    try {
        await mongoose_1.default.connect(index_1.config.MONGODB_URI, mongoOptions);
        console.log("Connected to MongoDB successfully");
    }
    catch (error) {
        console.error("MongoDB connection error:", error);
        if (index_1.config.NODE_ENV === "production") {
            process.exit(1);
        }
        else {
            console.warn("Continuing without MongoDB in development mode");
        }
    }
};
// MongoDB connection event handlers
mongoose_1.default.connection.on("connected", () => {
    console.log("Mongoose connected to MongoDB");
});
mongoose_1.default.connection.on("error", (err) => {
    console.error("Mongoose connection error:", err);
});
mongoose_1.default.connection.on("disconnected", () => {
    console.log("Mongoose disconnected from MongoDB");
});
// Graceful shutdown
process.on("SIGINT", async () => {
    await mongoose_1.default.connection.close();
    console.log("MongoDB connection closed through app termination");
    process.exit(0);
});
exports.default = connectMongoDB;
// Market Data Schema
const marketDataSchema = new mongoose_1.default.Schema({
    currencyPair: { type: String, required: true, index: true },
    buy: String,
    sell: String,
    low: String,
    high: String,
    open: String,
    last: String,
    vol: String,
    lastPrice: String,
    lowestAsk: String,
    highestBid: String,
    baseVolume: String,
    quoteVolume: String,
    priceChangePercent24h: String,
    highestPrice24h: String,
    lowestPrice24h: String,
    status: { type: String, enum: ["gainer", "loser", "no change"] },
    timestamp: { type: Date, default: Date.now, index: true },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});
// Add compound index for efficient queries
marketDataSchema.index({ currencyPair: 1, timestamp: -1 });
marketDataSchema.index({ timestamp: -1 });
// Update the updatedAt field on save
marketDataSchema.pre("save", function (next) {
    this.updatedAt = new Date();
    next();
});
exports.MarketDataModel = mongoose_1.default.model("MarketData", marketDataSchema);
// Aggregated Market Data Schema for historical data
const aggregatedMarketDataSchema = new mongoose_1.default.Schema({
    currencyPair: { type: String, required: true, index: true },
    period: { type: String, required: true }, // "1h", "1d", "1w", "1m"
    averagePrice: Number,
    minPrice: Number,
    maxPrice: Number,
    totalVolume: Number,
    count: Number,
    firstPrice: Number,
    lastPrice: Number,
    startTime: { type: Date, required: true, index: true },
    endTime: { type: Date, required: true, index: true },
    createdAt: { type: Date, default: Date.now },
});
// Compound index for efficient aggregated queries
aggregatedMarketDataSchema.index({ currencyPair: 1, period: 1, startTime: -1 });
exports.AggregatedMarketDataModel = mongoose_1.default.model("AggregatedMarketData", aggregatedMarketDataSchema);
//# sourceMappingURL=mongodb.js.map